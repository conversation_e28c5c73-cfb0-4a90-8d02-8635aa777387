package core.monitoring.common.test

import java.io.FileWriter

internal actual fun CLILogger.writeToFile(
    date: String,
    text: String
) {
    try {
        val context = androidContext as android.content.Context
        val file = java.io.File(context.externalCacheDir, "logs/$date.txt")
        if (!file.exists()) {
            file.parentFile?.mkdirs()
            file.createNewFile()
        }

        val writer = FileWriter(file, true)

        try {
            writer.write("$text\n")
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            writer.close()
        }
    } catch (ex: Exception) {
    }
}