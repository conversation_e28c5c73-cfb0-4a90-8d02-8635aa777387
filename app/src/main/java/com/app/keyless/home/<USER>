package com.app.keyless.home

import android.Manifest
import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageView
import androidx.cardview.widget.CardView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.app.keyless.BuildConfig
import com.app.keyless.R
import com.app.keyless.databinding.FragmentHomeBinding
import com.google.gson.Gson
import com.iseo.v364sdk.services.exception.V364SdkException
import com.iseo.v364sdk.services.mobilecredentialservice.IMobileCredentialService
import com.iseo.v364sdk.services.mobilecredentialservice.model.IMobileCredentialsInfo
import com.iseo.v364sdk.services.scanservice.IScanManagerService
import com.khoiron.actionsheets.ActionSheet
import com.khoiron.actionsheets.callback.ActionSheetCallBack
import core.caching.KeyValueCache
import core.lock.iseo.IseoHelper
import data.common.preferences.Preferences
import data.common.preferences.Roles
import data.network.android.models.ModelPlatformDetails
import data.utils.android.CommonValues
import data.utils.android.CommonValues.Companion.ADMIN
import data.utils.android.CommonValues.Companion.ALREADY_LOGIN
import data.utils.android.CommonValues.Companion.GUEST
import data.utils.android.CommonValues.Companion.INSTALLER
import data.utils.android.CommonValues.Companion.isNetworkAvailable
import data.utils.android.applications.ServiceProvider
import data.utils.android.interfaces.ClickFragments
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.OnActionYesNo
import feature.common.dialogs.ProgressDialogUtils
import feature.common.dialogs.defaultDialog
import feature.common.dialogs.dialogYesNo
import feature.home.installer.InstallerListFragment
import feature.home.properties.PropertiesHomeFragment
import feature.pm.addlock.AddLockMainActivity
import feature.settings.cards.ConfiguredCardsActivity
import feature.settings.common.MoreSettingsFragment
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.json.JSONObject
import org.koin.android.ext.android.get
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.TimeZone

class HomeFragment : Fragment() {

    private var whichSideUser: Int = 0
    private lateinit var fragment: InstallerListFragment
    // TODO private lateinit var fragmentAdmin: feature.home.locks.LockHomeFragment
    private lateinit var fragmentAdmin: Fragment
    var viewModel: HomeViewModel? = null
    lateinit var updateValue: ClickFragments
    lateinit var sharePrefs: SharedPreferenceUtils
    var isPopUp = false
    private lateinit var scanManagerService: IScanManagerService
    private lateinit var mobileCredentialService: IMobileCredentialService
    private var whichScreenInstaller = 0
    private lateinit var binding: FragmentHomeBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentHomeBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        lifecycleScope.launch {
            delay(2000)
            val window = requireActivity().window
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.statusBarColor = ContextCompat.getColor(
                requireActivity(),
                keyless.feature.common.R.color.colorAccent
            )
        }
        sharePrefs = SharedPreferenceUtils.getInstance(requireActivity())
        if (sharePrefs.token.isNotBlank() && sharePrefs.token != Preferences.authenticationToken.get()) {
            Preferences.authenticationToken.set(sharePrefs.token)
        }
        setTexts()
        initz()
        clickListeners()
        askPermission()
        if (arguments?.getString("openLockScreen") == "1") {
            openAddLock()
        } else if (arguments?.getString("openLockScreen") == "2") {
            if (isNetworkAvailable(requireContext())) {
                viewModel?.hitLocksApi(sharePrefs.token, sharePrefs.uuid)
            }
        }
        observeNotification()
    }

    private fun observeNotification() {
        (requireActivity() as DashboardActivity).totalNotifyLive.observe(viewLifecycleOwner) {
            if (Preferences.userRole.get() == GUEST) {
                if (sharePrefs.totalUnreadNotification != 0) {
                    binding.notificationCountGuest.isVisible = true
                    binding.notificationCountGuest.text =
                        sharePrefs.totalUnreadNotification.toString()
                } else {
                    binding.notificationCountGuest.isVisible = false
                }
            } else {
                if (sharePrefs.totalUnreadNotification != 0) {
                    binding.notificationCount.isVisible = true
                    binding.notificationCount.text = sharePrefs.totalUnreadNotification.toString()
                } else {
                    binding.notificationCount.isVisible = false
                }
            }
        }
    }

    private fun askPermission() {
        if (Preferences.userRole.get() == ADMIN) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                requestPermissions(
                    arrayOf(
                        Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.ACCESS_COARSE_LOCATION,
                        Manifest.permission.BLUETOOTH_CONNECT,
                        Manifest.permission.BLUETOOTH_SCAN
                    ),
                    50
                )
            } else {
                requestPermissions(
                    arrayOf(
                        Manifest.permission.ACCESS_FINE_LOCATION
                    ),
                    50
                )
            }
        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    if (sharePrefs.isDenied == 1) {
                        requestPermissions(
                            arrayOf(
                                Manifest.permission.ACCESS_FINE_LOCATION,
                                Manifest.permission.ACCESS_COARSE_LOCATION
                            ),
                            50
                        )
                    } else {
                        requestPermissions(
                            arrayOf(
                                Manifest.permission.ACCESS_FINE_LOCATION,
                                Manifest.permission.ACCESS_COARSE_LOCATION,
                                Manifest.permission.POST_NOTIFICATIONS
                            ),
                            50
                        )
                    }
                } else {
                    requestPermissions(
                        arrayOf(
                            Manifest.permission.ACCESS_FINE_LOCATION,
                            Manifest.permission.ACCESS_COARSE_LOCATION
                        ),
                        50
                    )
                }
            } else {
                requestPermissions(
                    arrayOf(
                        Manifest.permission.ACCESS_FINE_LOCATION
                    ),
                    50
                )
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 50) {
            if (Preferences.userRole.get() == ADMIN) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (
                        grantResults.isNotEmpty() &&
                        grantResults[0] == PackageManager.PERMISSION_GRANTED &&
                        grantResults[1] == PackageManager.PERMISSION_GRANTED &&
                        grantResults[2] == PackageManager.PERMISSION_GRANTED &&
                        grantResults[3] == PackageManager.PERMISSION_GRANTED
                    ) {
                        callApi()
                        apiForLinks()
                    } else {
                        showDialogForPermissions()
                    }
                } else {
                    if (
                        grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED
                    ) {
                        callApi()
                        apiForLinks()
                    } else {
                        showDialogForPermissions()
                    }
                }
            } else {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        if (sharePrefs.isDenied == 1) {
                            if (
                                grantResults.isNotEmpty() &&
                                grantResults[0] == PackageManager.PERMISSION_GRANTED &&
                                grantResults[1] == PackageManager.PERMISSION_GRANTED
                            ) {
                                callApi()
                                apiForLinks()
                            } else {
                                showDialogForPermissions()
                            }
                        } else {
                            if (
                                grantResults.isNotEmpty() &&
                                grantResults[0] == PackageManager.PERMISSION_GRANTED &&
                                grantResults[1] == PackageManager.PERMISSION_GRANTED &&
                                grantResults[2] == PackageManager.PERMISSION_GRANTED
                            ) {
                                sharePrefs.isDenied = 2
                                callApi()
                                apiForLinks()
                            } else if (
                                grantResults.isNotEmpty() &&
                                grantResults[0] == PackageManager.PERMISSION_GRANTED &&
                                grantResults[1] == PackageManager.PERMISSION_GRANTED &&
                                grantResults[2] == PackageManager.PERMISSION_DENIED
                            ) {
                                sharePrefs.isDenied = 1
                                callApi()
                                apiForLinks()
                            } else {
                                showDialogForPermissions()
                            }
                        }
                    } else {
                        if (
                            grantResults.isNotEmpty() &&
                            grantResults[0] == PackageManager.PERMISSION_GRANTED &&
                            grantResults[1] == PackageManager.PERMISSION_GRANTED
                        ) {
                            callApi()
                            apiForLinks()
                        } else {
                            showDialogForPermissions()
                        }
                    }
                } else {
                    if (
                        grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED
                    ) {
                        callApi()
                        apiForLinks()
                    } else {
                        showDialogForPermissions()
                    }
                }
            }
        }
    }

    private fun apiForLinks() {
        viewModel?.platformDetails(sharePrefs.token)?.observe(viewLifecycleOwner) {
            sharePrefs.plantName = it.plant_name
            sharePrefs.iseoUrl = it.iseo_url
            IseoHelper.saveCredentials(
                settings = get<KeyValueCache>(),
                iseoUrl = it.iseo_url,
                plantName = it.plant_name
            )
            refreshCredentials(it)
        }
    }

    private fun refreshCredentials(modelPlatformDetails: ModelPlatformDetails) {
        scanManagerService = ServiceProvider.scanManagerService
        mobileCredentialService = ServiceProvider.mobileCredentialService
        CoroutineScope(Dispatchers.Main).launch {
//            updateStateNeutral("REFRESHING")
            delay(500)
            withContext(Dispatchers.IO) {
                try {
//                    val url = "https://sirademo.iseov364.com"
                    mobileCredentialService.refreshMobileCredential(
                        modelPlatformDetails.iseo_url,
                        modelPlatformDetails.plant_name
                    )
                    val mobileCredentialInfo: IMobileCredentialsInfo =
                        mobileCredentialService.getMobileCredentials(modelPlatformDetails.iseo_url)

                    val message = "ResCode:" + mobileCredentialInfo.resCode + "\n" +
                        "ResultDetails:" + mobileCredentialInfo.resultDetails + "\n" +
                        "ValidityStart:" + mobileCredentialInfo.validityStart + "\n" +
                        "ValidityEnd:" + mobileCredentialInfo.validityEnd + "\n" +
                        "ValidationEnd:" + mobileCredentialInfo.validationEnd + "\n" +
                        "Credential count:" + mobileCredentialInfo.mobileCredentials.size
                    withContext(Dispatchers.Main) {
                        Log.e("REFRESH SUCCESS\n$message", "")
                    }
//                    getSharedPreferences("prefs", Context.MODE_PRIVATE).edit().putString("url", url).apply()
                } catch (e: V364SdkException) {
                    withContext(Dispatchers.Main) {
//                        updateState(e.v364SdkErrorCode)
                    }
//                    Log.e(TAG, "startService: ", e)
                }
            }
        }
    }

    private fun showDialogForPermissions() {
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        val locPermission = kotlin.runCatching {
            ActivityCompat.checkSelfPermission(
                requireActivity(),
                Manifest.permission.ACCESS_FINE_LOCATION
            )
        }.getOrNull()

        if (locPermission == -1) {
            var string = ""
            string = if (Preferences.userRole.get() == ADMIN) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    getString(keyless.data.utils.android.R.string.please_allow_permissions_to_continue)
                } else {
                    getString(keyless.data.utils.android.R.string.location_permission_needed)
                }
            } else {
                getString(keyless.data.utils.android.R.string.location_permission_needed)
            }
            if (activity != null && isAdded) {
                defaultDialog(
                    requireActivity(),
                    string,
                    object : OnActionOK {
                        override fun onClickData() {
                            runCatching {
                                startActivityForResult(
                                    Intent(
                                        Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                                        Uri.fromParts("package", requireActivity().packageName, null)
                                    ),
                                    10
                                )
                            }
                        }
                    }
                )
            }
        }
    }

    private fun setTexts() {
        binding.titlePage.text = getString(keyless.data.utils.android.R.string.home)
        binding.txtLocks.text = getString(keyless.data.utils.android.R.string.lock)
        binding.txtProperties.text = getString(keyless.data.utils.android.R.string.buildings)
    }

    private fun loadFragment() {
        fragmentAdmin = dashboardFragment()
        CommonValues.loadFragment(
            fragmentAdmin,
            supportFragmentManager = requireFragmentManager(),
            layoutId = R.id.frameContainerHome
        )
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        Log.d("Tag", "onSaveInstanceState Called")
    }

    private fun observerInit() {
        viewModel?.getResponseMain?.observe(viewLifecycleOwner) {
            sharePrefs.totalUnreadNotification = it.totalUnreadNotification
            if (Preferences.userRole.get() == GUEST) {
                if (sharePrefs.totalUnreadNotification != 0) {
                    binding.notificationCountGuest.isVisible = true
                    binding.notificationCountGuest.text = sharePrefs.totalUnreadNotification.toString()
                } else {
                    binding.notificationCountGuest.isVisible = false
                }
            } else {
                if (sharePrefs.totalUnreadNotification != 0) {
                    binding.notificationCount.isVisible = true
                    binding.notificationCount.text = sharePrefs.totalUnreadNotification.toString()
                } else {
                    binding.notificationCount.isVisible = false
                }
            }
        }

        // already login error
        viewModel?.errorLogout?.observe(viewLifecycleOwner) {
            Preferences.isLoggedInAnotherDevice.set(true)
            defaultDialog(
                requireActivity(),
                ALREADY_LOGIN(requireActivity()),
                object : OnActionOK {
                    override fun onClickData() {
                        updateValue.clicksIntents(1)
                    }
                }
            )
            binding.swipeRefresh.isRefreshing = false
        }

        viewModel?.getResponseLogout?.observe(viewLifecycleOwner) {
            updateValue.clicksIntents(1)
            binding.swipeRefresh.isRefreshing = false
        }
        viewModel?.progress?.observe(viewLifecycleOwner) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(requireContext(), true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
            binding.swipeRefresh.isRefreshing = false
        }
        viewModel?.error?.observe(viewLifecycleOwner) {
            if (!isPopUp) {
                if (it == "Unauthorized") {
                    isPopUp = true
                    Handler(Looper.getMainLooper())
                    lifecycleScope.launch(
                        CoroutineExceptionHandler { coroutineContext, throwable -> }
                    ) {
                        delay(2000)
                        defaultDialog(
                            requireActivity(),
                            getString(keyless.data.utils.android.R.string.your_session_expired),
                            object : OnActionOK {
                                override fun onClickData() {
                                    updateValue.clicksIntents(1)
                                }
                            }
                        )
                    }
                } else {
                    requireContext().toast(it)
                }
            }
            if (BuildConfig.DEBUG) {
                return@observe
            }
//            on start alarm
            val alarmON = sharePrefs.isAlarmON
            if (!alarmON) {
                // SetReminder.startAlarm(requireActivity())
            } else {
                // show 8 hour notification
                lifecycleScope.launch {
                    delay(2000)
                    if (sharePrefs.show8HoursWarning) {
                        defaultDialog(
                            requireActivity(),
                            getString(keyless.data.utils.android.R.string.please_connect_internet),
                            object : OnActionOK {
                                override fun onClickData() {
                                }
                            }
                        )
                    }
                }
            }

            binding.swipeRefresh.isRefreshing = false
        }
        viewModel?.getResponseHomeLock?.observe(viewLifecycleOwner) {
            val gson = Gson()
            val homeData: String = gson.toJson(it)
            sharePrefs.homeLockData(homeData)
            sharePrefs.isApiDone = true
            if (whichSideUser == 0) {
                fragmentAdmin = dashboardFragment()
                CommonValues.loadFragment(
                    fragmentAdmin,
                    supportFragmentManager = requireFragmentManager(),
                    layoutId = R.id.frameContainerHome
                )
            }
            if (sharePrefs.getLockHistoryBackup().isNotEmpty()) {
                viewModel?.updateLogs(requireActivity())
            }

            // SetReminder.stopAlarm(requireActivity())
            binding.swipeRefresh.isRefreshing = false
        }

        viewModel?.getResponsePropertiesLock?.observe(viewLifecycleOwner) {
            val gson = Gson()
            val homePropertiesData: String = gson.toJson(it)
            sharePrefs.homePropertiesData(homePropertiesData)
            sharePrefs.propertyCount = it.size
            binding.swipeRefresh.isRefreshing = false
        }
    }

    private fun initz() {
        viewModel = ViewModelProvider(this)[HomeViewModel::class.java]
        when (Preferences.userRole.get()) {
            ADMIN -> {
                binding.ivSettings.isVisible = false
                binding.ivSettings.setImageResource(keyless.data.utils.android.R.drawable.iv_logout)
                binding.mainFilter.isVisible = false
                binding.ivAddAdminLock.isVisible = true
                binding.changeLanguage.isVisible = false
                fragmentAdmin = dashboardFragment()
                CommonValues.loadFragment(
                    fragmentAdmin,
                    supportFragmentManager = requireFragmentManager(),
                    layoutId = R.id.frameContainerHome
                )
            }

            GUEST -> {
                if (sharePrefs.totalUnreadNotification != 0) {
                    binding.notificationCountGuest.isVisible = true
                    binding.notificationCountGuest.text = sharePrefs.totalUnreadNotification.toString()
                } else {
                    binding.notificationCountGuest.isVisible = false
                }
                binding.ivSettings.isVisible = true
                binding.changeLanguage.isVisible = false
                binding.notificationIconGuest.isVisible = true
                binding.ivSettings.setImageResource(keyless.presentation.common.R.drawable.iv_settings)
                binding.mainFilter.isVisible = false
                binding.ivAddAdminLock.isVisible = false
                val sdf = SimpleDateFormat("HH")
                sdf.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
                val str: Int = sdf.format(Date()).toInt()
                if (Preferences.firstName.get().isNotEmpty()) {
                    if (str < 12) {
                        binding.titlePage.text =
                            getString(
                            keyless.data.utils.android.R.string.text_good_morning
                        ) + " " + Preferences.firstName.get()
                    } else if (str < 17) {
                        binding.titlePage.text =
                            getString(
                            keyless.data.utils.android.R.string.text_good_afternoon
                        ) + " " + Preferences.firstName.get()
                    } else {
                        binding.titlePage.text =
                            getString(
                            keyless.data.utils.android.R.string.text_good_evening
                        ) + " " + Preferences.firstName.get()
                    }
                } else if (sharePrefs.name.isNotEmpty()) {
                    if (sharePrefs.name.contains(" ")) {
                        val splitName = sharePrefs.name.split(" ")
                        if (str < 12) {
                            binding.titlePage.text =
                                getString(keyless.data.utils.android.R.string.text_good_morning) + " " + splitName[0]
                        } else if (str < 17) {
                            binding.titlePage.text =
                                getString(keyless.data.utils.android.R.string.text_good_afternoon) + " " + splitName[0]
                        } else {
                            binding.titlePage.text =
                                getString(keyless.data.utils.android.R.string.text_good_evening) + " " + splitName[0]
                        }
                    } else {
                        if (str < 12) {
                            binding.titlePage.text = getString(keyless.data.utils.android.R.string.good_morning_guest)
                        } else if (str < 17) {
                            binding.titlePage.text = getString(keyless.data.utils.android.R.string.good_afternoon_guest)
                        } else {
                            binding.titlePage.text = getString(keyless.data.utils.android.R.string.good_evening_guest)
                        }
                    }
                } else {
                    if (str < 12) {
                        binding.titlePage.text = getString(keyless.data.utils.android.R.string.good_morning_guest)
                    } else if (str < 17) {
                        binding.titlePage.text = getString(keyless.data.utils.android.R.string.good_afternoon_guest)
                    } else {
                        binding.titlePage.text = getString(keyless.data.utils.android.R.string.good_evening_guest)
                    }
                }
                callHomeApi()
            }

            INSTALLER -> {
                binding.ivSettings.isVisible = true
                binding.changeLanguage.isVisible = true
                binding.ivSettings.setImageResource(keyless.data.utils.android.R.drawable.iv_logout)
                binding.mainFilter.isVisible = true
                binding.mainFilterInner.isVisible = false
                binding.constraintInstaller.isVisible = true
                fragment = InstallerListFragment()
                CommonValues.loadFragment(
                    fragment,
                    supportFragmentManager = requireFragmentManager(),
                    layoutId = R.id.frameContainerHome
                )
            }

            // Means PM case
            else -> {
                if (sharePrefs.totalUnreadNotification != 0) {
                    binding.notificationCount.isVisible = true
                    binding.notificationCount.text = sharePrefs.totalUnreadNotification.toString()
                } else {
                    binding.notificationCount.isVisible = false
                }
                binding.ivSettings.isVisible = false
                binding.mainFilter.isVisible = true
                binding.changeLanguage.isVisible = false
                binding.ivAddAdminLock.isVisible = false
                callHomeApi()
            }
        }

        binding.ivAdd.isVisible = !(
            Preferences.role.get() == Roles.VIEWER_ACCESS ||
                Preferences.role.get() == Roles.CUSTOMER_SERVICES ||
                Preferences.role.get() == Roles.SYSTEM_MANAGER
            )
    }

    private fun callHomeApi() {
        val empty = sharePrefs.getLockData().isEmpty()
        if (!empty) {
            loadFragment()
        }
        if (isNetworkAvailable(requireContext()) && !sharePrefs.isApiDone) {
            viewModel?.hitLocksApi(sharePrefs.token, sharePrefs.uuid)
        } else {
            loadFragment()
        }
    }

    override fun onResume() {
        super.onResume()
        if (CommonValues.refreshApi) {
            ProgressDialogUtils.getInstance().showProgress(requireContext(), true)
            CommonValues.refreshApi = false
            viewModel?.hitLocksApi(sharePrefs.token, sharePrefs.uuid)
            checkViewLocks()
        }
    }

    private fun clickListeners() {
        binding.swipeRefresh.setOnRefreshListener {
            if (isNetworkAvailable(requireActivity())) {
                if (Preferences.userRole.get() != INSTALLER && Preferences.userRole.get() != ADMIN) {
                    apiForLinks()
                    apiServerTime()
                    binding.swipeRefresh.isRefreshing = true
                } else if (Preferences.userRole.get() == ADMIN) {
                    // TODO Restore
                    // fragmentAdmin.updateAdminData()
                    binding.swipeRefresh.isRefreshing = false
//                viewModel?.hitAdminLocksApi(sharePrefs.token, sharePrefs.uuid, requireActivity())
                } else {
                    if (whichScreenInstaller == 0) {
                        fragment.update(whichScreenInstaller)
                    } else {
                        fragment.apiMaintenance(whichScreenInstaller)
                    }
                    binding.swipeRefresh.isRefreshing = false
                }
            } else {
                binding.swipeRefresh.isRefreshing = false
            }
        }

        binding.changeLanguage.setOnClickListener {
            val listLanguage = ArrayList<String>()
            listLanguage.add(getString(keyless.data.utils.android.R.string.txt_english))
            listLanguage.add(getString(keyless.data.utils.android.R.string.text_arabic))
            ActionSheet(requireActivity(), listLanguage)
                .setTitle(requireActivity().getString(keyless.data.utils.android.R.string.change_language))
                .setCancelTitle(getString(keyless.feature.common.R.string.text_cancel))
                .setColorTitle(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .setColorTitleCancel(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .setColorSelected(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .setColorData(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .create(object : ActionSheetCallBack {
                    override fun data(data: String, position: Int) {
                        resources.configuration
                        if (data == "English") {
                            sharePrefs.language = "en"
                        } else if (data == "Arabic") {
                            sharePrefs.language = "ar"
                        }
                        startActivity(
                            Intent(
                                requireActivity(),
                                DashboardActivity::class.java
                            ).putExtra("runSplash", false)
                        )
                        requireActivity().finish()
                    }
                })
        }

        binding.ivAddAdminLock.setOnClickListener {
            val data: ArrayList<String> = ArrayList()
            data.add(getString(keyless.feature.common.R.string.add_lock_options))
            data.add(getString(keyless.feature.common.R.string.add_rfid_card))
            ActionSheet(requireActivity(), data)
                .setTitle(getString(keyless.feature.common.R.string.options))
                .setCancelTitle(getString(keyless.feature.common.R.string.text_cancel))
                .setColorTitle(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .setColorTitleCancel(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .setColorSelected(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .setColorData(resources.getColor(keyless.feature.common.R.color.colorAccent))
                .create(object : ActionSheetCallBack {
                    override fun data(data: String, position: Int) {
                        when (position) {
                            0 -> {
                                startActivityForResult(
                                    Intent(
                                        requireActivity(),
                                        feature.home.admin.AddAdminLockActivity::class.java
                                    ),
                                    12
                                )
                            }

                            1 -> {
                                startActivityForResult(
                                    Intent(
                                        requireActivity(),
                                        feature.home.admin.SelectLockForCardActivity::class.java
                                    ),
                                    12
                                )
                            }
                        }
                    }
                })
        }

        binding.ivSettings.setOnClickListener {
            if (Preferences.userRole.get() == ADMIN || Preferences.userRole.get() == INSTALLER) {
                dialogYesNo(
                    requireActivity(),
                    getString(keyless.data.utils.android.R.string.logout),
                    getString(keyless.data.utils.android.R.string.are_you_sure_you_want_to_logout),
                    object : OnActionYesNo {
                        override fun onYes(view: View) {
                            viewModel?.logoutAccount(sharePrefs.token, requireActivity())
                        }

                        override fun onNo(view: View) {
                        }

                        override fun onClickData(view: View, data: String) {
                        }
                    }
                )
            } else {
                CommonValues.loadFragmentWithStack(
                    MoreSettingsFragment(),
                    supportFragmentManager = requireFragmentManager(),
                    layoutId = R.id.frameContainer
                )
            }
        }

        binding.viewLock.setOnClickListener {
            checkViewLocks()
            fragmentAdmin = dashboardFragment()
            CommonValues.loadFragment(
                fragmentAdmin,
                supportFragmentManager = requireFragmentManager(),
                layoutId = R.id.frameContainerHome
            )
            whichSideUser = 0
        }

        binding.viewLockInstaller.setOnClickListener {
            checkViewLockInstallation()
            whichScreenInstaller = 0
            fragment.update(whichScreenInstaller)
            binding.swipeRefresh.isRefreshing = false
        }

        binding.viewMaintenance.setOnClickListener {
            checkViewMaintenance()
            whichScreenInstaller = 1
            fragment.apiMaintenance(whichScreenInstaller)
        }

        binding.viewProperties.setOnClickListener {
            binding.ivLock.setImageResource(
                keyless.feature.common.R.drawable.iv_not_checked_grey
            )
            binding.ivProperties.setImageResource(
                keyless.feature.common.R.drawable.iv_checked_radio
            )
            CommonValues.loadFragment(
                PropertiesHomeFragment(),
                supportFragmentManager = requireFragmentManager(),
                layoutId = R.id.frameContainerHome
            )

            whichSideUser = 1
        }

        binding.ivAdd.setOnClickListener {
            if (Preferences.isAdminLogin()) {
                defaultDialog(
                    requireActivity(),
                    getString(keyless.data.utils.android.R.string.disabled_in_admin_mode),
                    object : OnActionOK {
                        override fun onClickData() {
                        }
                    }
                )
            } else {
                if (sharePrefs.propertyCount > 0) {
                    var includeRayonics = false
                    for (i in sharePrefs.getLockData()) {
                        if (i.lock.provider == "Keyless") {
                            includeRayonics = true
                            break
                        }
                    }

                    if (includeRayonics) {
                        commonDialogForAdd()
                    } else {
                        clickForAddLock()
                    }
                } else {
                    defaultDialog(
                        requireActivity(),
                        getString(keyless.data.utils.android.R.string.must_add_building),
                        object : OnActionOK {
                            override fun onClickData() {
                                updateValue.clicksIntents(2)
                            }
                        }
                    )
                }
            }
        }

        binding.notificationIcon.setOnClickListener {
            startActivityForResult(
                Intent(requireActivity(), feature.notifications.NotificationActivity::class.java),
                52
            )
        }

        binding.notificationIconGuest.setOnClickListener {
            startActivityForResult(
                Intent(requireActivity(), feature.notifications.NotificationActivity::class.java),
                52
            )
        }
    }

    private fun commonDialogForAdd() {
        val dialog = Dialog(requireContext())
        dialog.setContentView(keyless.feature.common.R.layout.layout_add_lock_dialog)
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog.setCancelable(true)
        dialog.setCanceledOnTouchOutside(true)
        dialog.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT
        )
        var cvAddLock = dialog.findViewById<CardView>(keyless.feature.common.R.id.cvAddLock)
        var cvMasterKey = dialog.findViewById<CardView>(keyless.feature.common.R.id.cvMasterKey)
        var cvConfigureCard = dialog.findViewById<CardView>(keyless.feature.common.R.id.cvConfigureCard)
        var closeDialog = dialog.findViewById<ImageView>(keyless.feature.common.R.id.closeDialog)
        cvAddLock.setOnClickListener {
            clickForAddLock()
            dialog.dismiss()
        }
        cvMasterKey.setOnClickListener {
            clickForAddMasterKey()
            dialog.dismiss()
        }
        cvConfigureCard.setOnClickListener {
            clickForConfigureCard()
            dialog.dismiss()
        }
        closeDialog.setOnClickListener {
            dialog.dismiss()
        }
        dialog.show()
    }

    private fun apiServerTime() {
        var time = "UTC"
        val request = Request.Builder()
            .url("https://www.timeapi.io/api/Time/current/zone?timeZone=$time")
            .build()
        val client = OkHttpClient.Builder().build()
        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                e.printStackTrace()
                viewModel?.hitLocksApi(sharePrefs.token, sharePrefs.uuid)
            }

            override fun onResponse(call: Call, response: Response) {
                if (response.isSuccessful) {
                    try {
                        val res = response.body!!.string()
                        val json = JSONObject(res)
                        CommonValues.serverDateTime = json.optString("dateTime")
                        lifecycleScope.launch(
                            CoroutineExceptionHandler { coroutineContext, throwable -> }
                        ) {
                            viewModel?.hitLocksApi(
                                sharePrefs.token,
                                sharePrefs.uuid
                            )
                        }
                        Log.e("dateeee", CommonValues.serverDateTime)
                    } catch (e: Exception) {
                        e.printStackTrace()
                        lifecycleScope.launch(
                            CoroutineExceptionHandler { coroutineContext, throwable -> }
                        ) {
                            viewModel?.hitLocksApi(
                                sharePrefs.token,
                                sharePrefs.uuid
                            )
                        }
                    }
                }
            }
        })
    }

    private fun checkViewMaintenance() {
        binding.ivLockInstaller.setImageResource(
            keyless.feature.common.R.drawable.iv_not_checked_grey
        )
        binding.ivMaintenance.setImageResource(
            keyless.feature.common.R.drawable.iv_checked_radio
        )
    }

    private fun checkViewLockInstallation() {
        binding.ivLockInstaller.setImageResource(keyless.feature.common.R.drawable.iv_checked_radio)
        binding.ivMaintenance.setImageResource(
            keyless.feature.common.R.drawable.iv_not_checked_grey
        )
    }

    private fun clickForConfigureCard() {
        startActivity(Intent(requireActivity(), ConfiguredCardsActivity::class.java))
    }

    private fun checkViewLocks() {
        binding.ivLock.setImageResource(keyless.feature.common.R.drawable.iv_checked_radio)
        binding.ivProperties.setImageResource(
            keyless.feature.common.R.drawable.iv_not_checked_grey
        )
    }

    private fun clickForAddMasterKey() {
        val intent = Intent(requireContext(), feature.masterkey.AddMasterKeyActivity::class.java)
        startActivityForResult(intent, 40)
    }

    private fun clickForAddLock() {
        val intent = Intent(context, AddLockMainActivity::class.java)
        startActivityForResult(intent, 40)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 40) {
            if (isNetworkAvailable(requireContext())) {
                viewModel?.hitLocksApi(sharePrefs.token, sharePrefs.uuid)
            }
        } else if (resultCode == 54) {
            if (isNetworkAvailable(requireContext())) {
                viewModel?.hitLocksApi(sharePrefs.token, sharePrefs.uuid)
            }
        } else if (requestCode == 10) {
            askPermission()
        } else if (resultCode == Activity.RESULT_OK) {
            fragmentAdmin = dashboardFragment()
            CommonValues.loadFragment(
                fragmentAdmin,
                supportFragmentManager = requireFragmentManager(),
                layoutId = R.id.frameContainerHome
            )
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        updateValue = context as ClickFragments
    }

    override fun onAttach(activity: Activity) {
        super.onAttach(activity)
        updateValue = activity as ClickFragments
    }

    fun callApi() {
        observerInit()
    }

    private fun openAddLock() {
        sharePrefs = SharedPreferenceUtils.getInstance(requireContext())
        if (sharePrefs.propertyCount > 0) {
            var includeRayonics = false
            for (i in SharedPreferenceUtils.getInstance(requireContext()).getLockData()) {
                if (i.lock.provider == "Keyless") {
                    includeRayonics = true
                    break
                }
            }

            if (includeRayonics) {
                commonDialogForAdd()
            } else {
                clickForAddLock()
            }
        } else {
            defaultDialog(
                requireActivity(),
                getString(keyless.data.utils.android.R.string.must_add_building),
                object : OnActionOK {
                    override fun onClickData() {
                        updateValue.clicksIntents(2)
                    }
                }
            )
        }
    }
    
    private fun dashboardFragment(): Fragment {
        // return feature.home.locks.LockHomeFragment()
        return presentation.home.dashboard.feature.DashboardFragment()
    }
}