name: "Delete Deployments"
description: "Delete unnecessary deployments"

runs:
  using: "composite"
  steps:
    - name: Delete job deployments
      uses: actions/github-script@v6
      with:
        script: |
          const deployments = await github.rest.repos.listDeployments({
            owner: context.repo.owner,
            repo: context.repo.repo,
            sha: context.sha
          });
          await Promise.all(
            deployments.data.map(async (deployment) => {
              await github.rest.repos.createDeploymentStatus({ 
                owner: context.repo.owner, 
                repo: context.repo.repo, 
                deployment_id: deployment.id, 
                state: 'inactive' 
              });
              return github.rest.repos.deleteDeployment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                deployment_id: deployment.id
              });
            })
          );