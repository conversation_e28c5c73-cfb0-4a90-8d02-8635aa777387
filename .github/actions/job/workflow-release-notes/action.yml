name: "Generate release notes"
description: "Generate release notes from last successful workflow run"
inputs:
  shell:
    required: true
    description: "Shell to use depending on runner os"

  githubToken:
    required: true
    description: "GitHub token"
  repository:
    required: true
    description: "Repository name"
  sedVariant:
    required: true
    description: "Sed variant to use depending on runner os"
  workflowFileName:
    required: true
    description: "Workflow file name"


runs:
  using: "composite"
  steps:
    - name: Generate release notes
      id: generateReleaseNotes
      run: |
        URL=$(echo "https://api.github.com/repos/${{ inputs.repository }}/actions/workflows/${{ inputs.workflowFileName }}/runs?per_page=1&status=success" | ${{ inputs.sedVariant }} "s/ /%20/g")
        touch workflow_release_notes.txt
        CURL_RESULT=$(curl -v --silent -H 'Authorization: Bearer ${{ inputs.githubToken }}' -H 'Accept: application/vnd.github.v3+json' "$URL" 2>&1 | grep head_sha || echo "Init" )
        AWK_RESULT=$(echo "$CURL_RESULT" | awk '{print $2}' | ${{ inputs.sedVariant }} 's/,//g' | ${{ inputs.sedVariant }} 's/"//g')
        if [ -z "$AWK_RESULT" ]; then echo "INIT" > workflow_release_notes.txt; else git log $AWK_RESULT..$GITHUB_REF_NAME --pretty=format:"%s" > workflow_release_notes.txt; fi
      shell: ${{ inputs.shell }}