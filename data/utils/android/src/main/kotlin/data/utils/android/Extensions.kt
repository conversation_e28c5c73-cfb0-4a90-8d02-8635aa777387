package data.utils.android

import android.app.Activity
import android.content.ContentResolver
import android.content.Context
import android.content.Intent
import android.database.Cursor
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Environment
import android.os.Parcelable
import android.os.SystemClock
import android.provider.MediaStore
import android.provider.OpenableColumns
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.text.HtmlCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import com.google.android.material.snackbar.Snackbar
import com.vdx.designertoast.DesignerToast
import keyless.data.utils.android.R
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.util.regex.Pattern

fun View.clickWithDebounce(debounceTime: Long = 600L, action: () -> Unit) {
    this.setOnClickListener(object : View.OnClickListener {
        private var lastClickTime: Long = 0

        override fun onClick(v: View) {
            if (SystemClock.elapsedRealtime() - lastClickTime < debounceTime) {
                return
            } else {
                action()
            }

            lastClickTime = SystemClock.elapsedRealtime()
        }
    })
}

fun View.snackbar(
    text: CharSequence,
    duration: Int = Snackbar.LENGTH_SHORT,
    init: Snackbar.() -> Unit = {}
): Snackbar {
    val snack = Snackbar.make(this, text, duration)
    snack.init()
    snack.show()
    return snack
}

fun Fragment.snackbar(
    text: CharSequence,
    duration: Int = Snackbar.LENGTH_LONG,
    init: Snackbar.() -> Unit = {}
): Snackbar {
    return view!!.snackbar(text, duration, init)
}

fun Context.toast(message: String) {
    DesignerToast.Custom(
        this, message,
        Gravity.BOTTOM, Toast.LENGTH_LONG, R.drawable.bg_toast, 14,
        "#ffffff", R.mipmap.ic_launcher_round, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT
    )
}

fun Activity.toast(message: String) {
    DesignerToast.Custom(
        this, message,
        Gravity.BOTTOM, Toast.LENGTH_LONG, R.drawable.bg_toast, 14,
        "#ffffff", R.mipmap.ic_launcher_round, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT
    )
}

fun String.isValidEmail(): Boolean {
    val expression = "^[\\w\\.-]+@([\\w\\-]+\\.)+[A-Z]{2,10}$"
    val pattern = Pattern.compile(expression, Pattern.CASE_INSENSITIVE)
    val matcher = pattern.matcher(this)
    return matcher.matches()
}

fun String.isNotValidEmail(): Boolean {
    val expression = "^[\\w\\.-]+@([\\w\\-]+\\.)+[A-Z]{2,10}$"
    val pattern = Pattern.compile(expression, Pattern.CASE_INSENSITIVE)
    val matcher = pattern.matcher(this)
    return !matcher.matches()
}

fun String.isValidMobile(): Boolean = !isNotValidMobile()
fun EditText.getString(): String = this.text.toString()
fun String.isNotValidMobile(): Boolean = this.length !in 7..15
fun getRealPathFromURI(context: Context, contentURI: Uri): String {
    val result: String
    val cursor = context.contentResolver.query(contentURI, null, null, null, null)
    if (cursor == null) { // Source is Dropbox or other similar local file path
        result = contentURI.path!!
    } else {
        cursor.moveToFirst()
        val idx = cursor.getColumnIndex(MediaStore.Images.ImageColumns.DATA)
        result = cursor.getString(idx)
        cursor.close()
    }
    return result
}

val String.isEmail: Boolean
    get() {
        val p = "^(\\w)+(\\.\\w+)*@(\\w)+((\\.\\w+)+)\$".toRegex()
        return matches(p)
    }

fun ContentResolver.getFileName(fileUri: Uri): String {
    var name = ""
    val returnCursor = this.query(fileUri, null, null, null, null)
    if (returnCursor != null) {
        val nameIndex = returnCursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
        returnCursor.moveToFirst()
        name = returnCursor.getString(nameIndex)
        returnCursor.close()
    }
    return name
}

fun Fragment.hideKeyboard() {
    view?.let { activity?.hideKeyboard(it) }
}

fun Activity.hideKeyboard() {
    hideKeyboard(currentFocus ?: View(this))
}

fun Context.hideKeyboard(view: View) {
    val inputMethodManager = getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
    inputMethodManager.hideSoftInputFromWindow(view.windowToken, 0)
}

fun AppCompatActivity.popBackStack() {
    hideKeyboard()
    supportFragmentManager.popBackStack()
}

fun AppCompatActivity.popBackStackInclusive() {
    hideKeyboard()
    if (supportFragmentManager.backStackEntryCount > 0) {
        supportFragmentManager.popBackStack(
            supportFragmentManager.getBackStackEntryAt(0).id,
            FragmentManager.POP_BACK_STACK_INCLUSIVE
        )
    }
}

inline fun FragmentManager.inTransaction(func: FragmentTransaction.() -> FragmentTransaction) {
    beginTransaction().func().commit()
}

fun AppCompatActivity.addFragment(fragment: Fragment, frameId: Int) {
    supportFragmentManager.inTransaction { add(frameId, fragment, fragment.javaClass.simpleName) }
}

fun AppCompatActivity.replaceFragment(fragment: Fragment, frameId: Int) {
    supportFragmentManager.inTransaction {
        replace(
            frameId,
            fragment,
            fragment.javaClass.simpleName
        )
    }
}

fun AppCompatActivity.replaceFragment(
    fragment: Fragment,
    frameId: Int,
    addToStack: Boolean,
    clearBackStack: Boolean
) {
    supportFragmentManager.inTransaction {
        if (clearBackStack && supportFragmentManager.backStackEntryCount > 0) {
            val first = supportFragmentManager.getBackStackEntryAt(0)
            supportFragmentManager.popBackStack(first.id, FragmentManager.POP_BACK_STACK_INCLUSIVE)
        }

        if (addToStack) {
            replace(frameId, fragment, fragment.javaClass.simpleName)
                .addToBackStack(fragment.javaClass.simpleName)
        } else {
            replace(frameId, fragment, fragment.javaClass.simpleName)
        }
    }
}

fun AppCompatActivity.addFragment(fragment: Fragment, frameId: Int, addToStack: Boolean) {
    supportFragmentManager.inTransaction {
        if (addToStack) {
            add(frameId, fragment, fragment.javaClass.simpleName)
                .addToBackStack(fragment.javaClass.simpleName)
        } else {
            add(frameId, fragment)
        }
    }
}

fun AppCompatActivity.getCurrentFragment(): Fragment? {
    val fragmentManager = supportFragmentManager
    var fragmentTag: String? = ""

    if (fragmentManager.backStackEntryCount > 0) {
        fragmentTag =
            fragmentManager.getBackStackEntryAt(fragmentManager.backStackEntryCount - 1).name
    }

    return fragmentManager.findFragmentByTag(fragmentTag)
}

fun AppCompatActivity.getBackStackEntryCount(): Int {
    return supportFragmentManager.backStackEntryCount
}

fun Intent.addExtra(key: String, value: Any?) {
    when (value) {
        is Long -> putExtra(key, value)
        is String -> putExtra(key, value)
        is Boolean -> putExtra(key, value)
        is Float -> putExtra(key, value)
        is Double -> putExtra(key, value)
        is Int -> putExtra(key, value)
        is Parcelable -> putExtra(key, value)
    }
}

inline fun <reified T> Activity.getExtra(extra: String): T? {
    return intent.extras?.get(extra) as? T?
}

fun getRequestBodyForField(field: String): RequestBody {
    return field.toRequestBody("text/plain".toMediaTypeOrNull())
}

fun getProfileMultiPartImage(imageKey: String, imageFile: File?): MultipartBody.Part? {
    return if (imageFile == null) {
        null
    } else {
        MultipartBody.Part.createFormData(
            imageKey,
            imageFile.name,
            imageFile.asRequestBody("image/*".toMediaTypeOrNull())
        )
    }
}

val IMAGE_MAX_SIZE = 1024
fun compressImageFile(context: Context?, pathUri: Uri): File {
    var b: Bitmap? = null

    var realPath: String? =
        getRealPathFromURI(context!!, pathUri)
    var f: File = File(realPath)

// Decode image size
    var o: BitmapFactory.Options = BitmapFactory.Options()
    o.inJustDecodeBounds = true

    var fis: FileInputStream
    try {
        fis = FileInputStream(f)
        BitmapFactory.decodeStream(fis, null, o)
        fis.close()
    } catch (e: FileNotFoundException) {
        e.printStackTrace()
    } catch (e: IOException) {
        e.printStackTrace()
    }

    var scale = 1
    if (o.outHeight > IMAGE_MAX_SIZE || o.outWidth > IMAGE_MAX_SIZE) {
        scale = Math.pow(
            2.0,
            Math.ceil(
                Math.log(
                    IMAGE_MAX_SIZE / Math.max(
                        o.outHeight,
                        o.outWidth
                    ).toDouble()
                ) / Math.log(0.5)
            )
        ).toInt()
    }

// Decode with inSampleSize
    var o2: BitmapFactory.Options = BitmapFactory.Options()
    o2.inSampleSize = scale
    try {
        fis = FileInputStream(f)
        b = BitmapFactory.decodeStream(fis, null, o2)
        fis.close()
    } catch (e: FileNotFoundException) {
        e.printStackTrace()
    } catch (e: IOException) {
        e.printStackTrace()
    }

    var destFile = File(getImageFilePath())
    try {
        var out: FileOutputStream = FileOutputStream(destFile)
        b?.compress(Bitmap.CompressFormat.PNG, 90, out)
        out.flush()
        out.close()
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return destFile
}

fun getRealPathFromURI2(context: Context, contentURI: Uri): String {
    var result = ""
    val cursor: Cursor? = context.contentResolver.query(contentURI, null, null, null, null)
    if (cursor == null) { // Source is Dropbox or other similar local file path
        result = contentURI.path.toString()
    } else {
        cursor.moveToFirst()
        val idx = cursor.getColumnIndex(MediaStore.Images.ImageColumns.DATA)
        result = cursor.getString(idx)
        cursor.close()
    }
    return result
}

fun getImageFilePath(): String {
    val file = Environment.getExternalStoragePublicDirectory(
        Environment.DIRECTORY_PICTURES + "/CareerPortalApp"
    )
    if (!file.exists()) {
        file.mkdirs()
    }
    return file.absolutePath + "/IMG_" + System.currentTimeMillis() + ".jpg"
}

fun getMultipartBody(filePath: String, fileName: String): MultipartBody.Part? {
    var multiPartBody: MultipartBody.Part? = null
    if (filePath.isNotEmpty()) {
        val file = File(filePath)
        if (file.exists()) {
            val requestBodyProfileImage = file.asRequestBody("image/*".toMediaTypeOrNull())
            multiPartBody =
                MultipartBody.Part.createFormData(fileName, file.name, requestBodyProfileImage)
        }
    }
    if (multiPartBody == null) {
        Log.e("TAG", "getMultipartBody: nullllllll")
    }
    return multiPartBody
}

fun TextView.setHtmlText(vararg pairs: Pair<Any, String>) {
    val string = pairs.map { pair ->
        "<font color=${pair.second}>${pair.first}</font>"
    }.reduce { acc, s -> acc + s }
//    this.text= Html.fromHtml(string.replace("\n", "<br>"))
    this.text = HtmlCompat.fromHtml(string.replace("\n", "<br>"), HtmlCompat.FROM_HTML_MODE_LEGACY)
}