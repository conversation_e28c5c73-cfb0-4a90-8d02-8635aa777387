package data.utils.android
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import keyless.data.utils.android.R
import java.util.ArrayList

class DataAdapter : RecyclerView.Adapter<DataAdapter.DataViewHolder>() {
    private val mDatas: MutableList<String>?
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): DataViewHolder {
        return DataViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.adapter_data, parent, false)
        )
    }

    override fun getItemCount(): Int {
        return mDatas?.size ?: 0
    }

    fun addItems(data: String) {
        if (data.isEmpty()) {
            return
        }
        mDatas!!.add(data)
        notifyItemRangeInserted(mDatas.size, 1)
    }

    fun clean() {
        mDatas!!.clear()
        notifyDataSetChanged()
    }

    override fun onBindViewHolder(holder: DataViewHolder, position: Int) {
        holder.mTxtAdapterData.text = mDatas!![position]
    }

    inner class DataViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        var mTxtAdapterData: TextView

        init {
            mTxtAdapterData = view.findViewById<View>(R.id.txt_adapter_data) as TextView
        }
    }

    init {

        mDatas = ArrayList()
    }
}