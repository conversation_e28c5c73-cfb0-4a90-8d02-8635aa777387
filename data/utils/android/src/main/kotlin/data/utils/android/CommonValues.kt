package data.utils.android

import android.content.Context
import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MarkerOptions
import data.network.android.models.IconModel
import data.utils.android.applications.Applications
import data.utils.android.datetime.DateTime
import data.utils.android.device.Device
import core.common.encryption.Encryption
import data.utils.android.maps.Maps
import keyless.data.utils.android.R

// To Show Toast

class CommonValues {

    companion object {

        var itemForFlag: String = "971"
        var itemForCountryName: String = "AE"
        var serverDateTime: String = ""
        var adminTempToken: String = ""
        var adminTempRole: String = ""
        var adminTempUserId: String = ""
        var adminTempUserType: String = ""
        val iseo: String = "ISEO"
        val messerSchimtt: String = "Messerschmitt"
        val keyless: String = "Keyless"
        val oji: String = "Oji"
        val linko: String = "Linko"
        val lockWise = "LockWise"
        val tedee = "Tedee"
        val TERMS_URL = "https://keyless.ae/terms-and-conditions/"
        val PRIVACY_URL = "https://keyless.ae/privacy-policy/"
        val LOCK_UPDATE: String = "LOCK_UPDATE"
        val NOTIFICATION_UPDATE: String = "NOTIFICATION_UPDATE"
        val DATE_FORMAT: String = "dd MMM, yy hh:mm a"
        var refreshApi: Boolean = false
        const val ADD_ADAPTER = 1
        const val EXTRA_C_BLE_NAME = "RC"
        const val EXTRA_D_BLE_NAME = "RD"
        const val EXTRA_B_BLE_NAME = "RB06"
        const val EXTRA_E_BLE_NAME = "REC"
        const val EXTRA_S_BLE_NAME = "RS02102"
        const val EXTRA_BLE_NAME = "BD"
        const val PREFERENCE_NAME = "KEYLESS_SAVE"
        const val PERSISTABLE_PREFERENCE_NAME = "KEYLESS_SAVE_MANAGER"
        const val ADMIN = "Admin"
        const val GUEST = "Guest"
        const val OWNER = "owner"
        const val INTEGRATOR = "Integrator"
        const val INSTALLER = "Installer"
        private var mDataAdapter: DataAdapter? = null
        private var mBleLayoutManager: LinearLayoutManager? = null
        private var mLayoutBleData: RecyclerView? = null
        val REQUEST_ENABLE_BT = 1
        fun ALREADY_LOGIN(context: Context): String {
            return context.getString(R.string.login_again_here_to_continue)
        }

        fun LOCK_INFO_MSG(context: Context): String {
            return context.getString(R.string.privacy_mode_on)
        }

        fun NOTIFICATION24(context: Context): String {
            return context.getString(R.string.please_connect_internet_24_hr_notification)
        }

        fun showMessage(data: String?, context: Context) {
            addAdapterItem(data, context)
        }

        fun directionStockMap(context: Context, lat: String?, lng: String?) {
            Maps.directionStockMap(context, lat, lng)
        }

        fun addCutomMarker(context: Context, latLng: LatLng, iconList: ArrayList<IconModel>?): MarkerOptions {
            return Maps.addCustomMarker(context, latLng, iconList)
        }

        private fun addAdapterItem(data: String?, context: Context) {
            if (data!!.isNotEmpty()) {
                mDataAdapter?.addItems(data)
                mBleLayoutManager = LinearLayoutManager(context)
                mBleLayoutManager?.reverseLayout = true
                mBleLayoutManager?.stackFromEnd = true
                mDataAdapter = DataAdapter()
                mLayoutBleData?.adapter = mDataAdapter
                mLayoutBleData?.layoutManager = mBleLayoutManager
                mLayoutBleData?.itemAnimator = DefaultItemAnimator()
                mLayoutBleData?.scrollToPosition(mDataAdapter?.itemCount!! - 1)
            }
        }

        fun loadFragment(
            fragment: Fragment,
            mbundle: Bundle? = null,
            supportFragmentManager: FragmentManager,
            layoutId: Int
        ) {
            if (mbundle != null) fragment.arguments = mbundle
            val transaction = supportFragmentManager.beginTransaction()
            transaction.replace(layoutId, fragment).commitAllowingStateLoss()
        }

        fun loadFragmentWithStack(
            fragment: Fragment,
            mbundle: Bundle? = null,
            supportFragmentManager: FragmentManager,
            layoutId: Int
        ) {
            if (mbundle != null) fragment.arguments = mbundle
            val transaction = supportFragmentManager.beginTransaction()
            transaction.replace(layoutId, fragment).addToBackStack(null) // ("$fragment")
                .commitAllowingStateLoss()
        }

        fun addFragment(
            fragment: Fragment,
            supportFragmentManager: FragmentManager,
            layoutId: Int,
            tag: String? = null
        ) {
            if (tag == null) {
                supportFragmentManager.beginTransaction()
                    .setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN)
                    .add(layoutId, fragment, null).commit()
            } else {
                supportFragmentManager.beginTransaction()
                    .setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN)
                    .add(layoutId, fragment, null).addToBackStack(null).commit()
            }
        }

        fun isNetworkAvailable(context: Context): Boolean {
            return Device.isNetworkAvailable(context)
        }

        fun isBluetoothEnabled(): Boolean {
            return Device.isBluetoothEnabled()
        }

        fun openPhone(context: Context, number: String) {
            Applications.dialPhone(context, number)
        }

        fun formattedDateOnlyEn(validFrom: String): String {
            return DateTime.formattedDateOnlyEn(validFrom)
        }

        fun formateTimeDate(validFrom: String, context: Context): String {
            return DateTime.formatTimeDate(validFrom, context)
        }

        fun formatTimeDateSimple(validFrom: String): String {
            return DateTime.formatTimeDateSimple(validFrom)
        }

        fun formatOnlyDate(validFrom: String): String {
            return DateTime.formatOnlyDate(validFrom)
        }

        fun formatOnlyDateWithoutTimeZone(validFrom: String): String {
            return DateTime.formatOnlyDateWithoutTimeZone(validFrom)
        }

        fun formatDdMmYyyyy(validFrom: String, context: Context): String {
            return DateTime.formatDdMmYyyyy(validFrom, context)
        }

        fun formateCompare(validFrom: String): String {
            return DateTime.formatCompare(validFrom)
        }

        fun formateTimeDate1(validFrom: String, context: Context): String {
            return DateTime.formatTimeDate1(validFrom, context)
        }

        fun formatDateEdit(validFrom: String): String {
            return DateTime.formatDateEdit(validFrom)
        }

        fun dateForLockHistory(): String {
            return DateTime.dateForLockHistory()
        }

        fun dateFor(dateMain: String): String {
            return DateTime.dateFor(dateMain)
        }

        fun dateForWithoutTimeZone(dateMain: String): String {
            return DateTime.dateForWithoutTimeZone(dateMain)
        }

        fun dateForHour(dateMain: String): String {
            return DateTime.dateForHour(dateMain)
        }

        fun dateForHourWithoutTimezone(dateMain: String): String {
            return DateTime.dateForHourWithoutTimezone(dateMain)
        }

        fun dateForMin(dateMain: String): String {
            return DateTime.dateForMin(dateMain)
        }

        fun dateForMinWithoutTimeZone(dateMain: String): String {
            return DateTime.dateForMinWithoutTimeZone(dateMain)
        }

        fun dateZformat(dateMain: String, context: Context): String {
            return DateTime.dateZformat(dateMain, context)
        }

        fun onWhatsAppIntent(context: Context, smsNumber: String) {
            Applications.onWhatsAppIntent(context, smsNumber)
        }

        fun getDeviceNameApi(): String {
            return Device.getDeviceNameApi()
        }

        fun isRooted(context: Context?): Boolean {
            return Device.isRooted(context)
        }

        fun decrypt(cipher: String?, secretKey: String): String? {
            return Encryption.decrypt(cipher, secretKey)
        }
    }
}