package data.utils.android.database

import android.os.Parcel
import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "ModelErrorData")
class ModelErrorData() : Parcelable {

    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    var id: Int = 0

    @ColumnInfo(name = "actionType")
    lateinit var actionType: String

    @ColumnInfo(name = "lockInfoData")
    lateinit var lockInfoData: String

    @ColumnInfo(name = "lockInternalId")
    lateinit var lockInternalId: String

    @ColumnInfo(name = "message")
    lateinit var message: String

    @ColumnInfo(name = "timeStampDate")
    var timeStampDate: Long = 0L

    @ColumnInfo(name = "provider")
    lateinit var provider: String

    constructor(parcel: Parcel) : this() {
        id = parcel.readInt()
        actionType = parcel.readString()!!
        lockInfoData = parcel.readString()!!
        lockInternalId = parcel.readString()!!
        message = parcel.readString()!!
        timeStampDate = parcel.readLong()
        provider = parcel.readString()!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(id)
        parcel.writeString(actionType)
        parcel.writeString(lockInfoData)
        parcel.writeString(lockInternalId)
        parcel.writeString(message)
        parcel.writeLong(timeStampDate)
        parcel.writeString(provider)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ModelErrorData> {
        override fun createFromParcel(parcel: Parcel): ModelErrorData {
            return ModelErrorData(parcel)
        }

        override fun newArray(size: Int): Array<ModelErrorData?> {
            return arrayOfNulls(size)
        }
    }
}