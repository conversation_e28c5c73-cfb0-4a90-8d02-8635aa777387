package data.keyless.lock

import core.common.serialization.json
import core.http.client.HttpClient
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.http.unwrap
import data.keyless.authentication.models.SendOtpResponse
import data.keyless.utils.bearer
import data.keyless.utils.delete
import data.keyless.utils.get
import data.network.android.AdminLocksResponse

class LockRepository(
    private val client: HttpClient,
    private val logger: Logger,
    private val hostname: String
) {
    suspend fun adminDeleteKeylessLock(lockId: String, token: String) = logger.async {
        val url = "$hostname/locks/delete/$lockId"
        val request = delete(url, bearer(token))

        return@async client.request(request).unwrap<SendOtpResponse>()
    }

    suspend fun adminLocks(page: Int, search: String, token: String) = logger.async {
        val url = "$hostname/admin/locks/chinese?page=$page&per_page=10&keyword=$search"
        val request = get(url, bearer(token))

        return@async client.request(request).unwrap<AdminLocksResponse>()
    }
}
