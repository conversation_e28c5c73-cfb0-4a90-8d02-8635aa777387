package data.keyless.lock

import core.common.serialization.json
import core.http.client.HttpClient
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.http.unwrap
import data.keyless.authentication.models.SendOtpResponse
import data.keyless.utils.bearer
import data.keyless.utils.delete
import data.keyless.utils.get

class LockRepository(
    private val client: HttpClient,
    private val logger: Logger,
    private val hostname: String
) {
    suspend fun adminDeleteKeylessLock(request: AdminDeleteKeylessLockRequest, token: String) = logger.async {
        val url = "$hostname/locks/delete/${request.lockId}"
        val httpRequest = delete(url, bearer(token))

        return@async client.request(httpRequest).unwrap<SendOtpResponse>()
    }

    suspend fun adminLocks(request: AdminLocksRequest, token: String) = logger.async {
        val url = "$hostname/admin/locks/chinese?page=${request.page}&per_page=${request.perPage}&keyword=${request.keyword}"
        val httpRequest = get(url, bearer(token))

        return@async client.request(httpRequest).unwrap<AdminLocksResponse>()
    }
}
