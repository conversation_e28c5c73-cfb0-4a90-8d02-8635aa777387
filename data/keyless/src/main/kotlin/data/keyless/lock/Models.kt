package data.keyless.lock

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Request model for admin delete keyless lock operation.
 * Since this is a DELETE operation, only the lock ID is needed as a path parameter.
 */
@Serializable
data class AdminDeleteKeylessLockRequest(
    val lockId: String
)

/**
 * Request model for admin locks operation.
 * Used for fetching paginated list of admin locks with search functionality.
 */
@Serializable
data class AdminLocksRequest(
    val page: Int,
    @SerialName("per_page")
    val perPage: Int = 10,
    val keyword: String
)

/**
 * Response model for admin locks operation.
 * Contains paginated list of admin locks with metadata.
 */
@Serializable
data class AdminLocksResponse(
    val locks: List<AdminLockItem> = emptyList(),
    val success: Boolean = false,
    @SerialName("total_locks")
    val totalLocks: Int = 0
)

/**
 * Individual admin lock item containing lock details and allocation information.
 */
@Serializable
data class AdminLockItem(
    @SerialName("alloted_to")
    val allotedTo: AllotedTo = AllotedTo(),
    val lock: LockDetails = LockDetails()
)

/**
 * Information about who the lock is allotted to.
 */
@Serializable
data class AllotedTo(
    val id: String = "",
    val name: String = ""
)

/**
 * Detailed lock information including all lock properties and metadata.
 */
@Serializable
data class LockDetails(
    @SerialName("__v")
    val version: Int = 0,
    @SerialName("_id")
    val id: String = "",
    @SerialName("access_key")
    val accessKey: String = "",
    @SerialName("encrypted_key")
    val encryptedKey: String = "",
    val alloted: Boolean = false,
    @SerialName("battery_level")
    val batteryLevel: Int = 0,
    val createdAt: String = "",
    val desc: String = "",
    val image: String = "",
    @SerialName("lock_uid")
    val lockUid: String = "",
    val name: String = "",
    val provider: String = "",
    val status: Int = 0,
    @SerialName("inventory_status")
    val inventoryStatus: Int = 0,
    @SerialName("unique_key")
    val uniqueKey: String = "",
    @SerialName("internal_id")
    val internalId: String = ""
)