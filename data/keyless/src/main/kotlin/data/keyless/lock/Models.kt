package data.keyless.lock

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Request model for admin delete keyless lock operation.
 * Since this is a DELETE operation, only the lock ID is needed as a path parameter.
 */
@Serializable
data class AdminDeleteKeylessLockRequest(
    val lockId: String
)

/**
 * Request model for admin locks operation.
 * Used for fetching paginated list of admin locks with search functionality.
 */
@Serializable
data class AdminLocksRequest(
    val page: Int,
    @SerialName("per_page")
    val perPage: Int = 10,
    val keyword: String
)