package data.keyless.home

import core.common.serialization.json
import core.http.client.HttpClient
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.http.unwrap
import data.common.now
import data.common.preferences.Preferences
import data.keyless.utils.authentication
import data.keyless.utils.post
import kotlinx.serialization.json.encodeToJsonElement

class HomeRepository(
    private val client: HttpClient,
    private val logger: Logger,
    private val hostname: String
) {

    suspend fun fetch(request: UserHomePostRequest) = logger.async {
        val url = "$hostname/user/home"
        val request = post(url, json.encodeToJsonElement(request), authentication())
        val response = client.request(request).unwrap<UserHomeResponse>()

        Preferences.locksJson.set(json.encodeToString(response))
        Preferences.lastOnlineLocksFetchDateTime.set(now().toString())

        return@async response
    }
}

val Preferences.locks: UserHomeResponse
    get() = json.decodeFromString(locksJson.get())