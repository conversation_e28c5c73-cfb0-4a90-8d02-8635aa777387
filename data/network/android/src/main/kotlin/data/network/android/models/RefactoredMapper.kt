package data.network.android.models

import data.network.android.LocksListResponse
import data.lock.common.lock.models.lock.Icon
import data.lock.common.lock.models.lock.Lock
import data.lock.common.lock.models.lock.LockAssignment
import data.lock.common.lock.models.lock.Property
import data.lock.common.lock.models.lock.PropertyDetails
import data.lock.common.lock.models.lock.TimeProfileId
import data.lock.common.lock.models.lock.TimeRange
import data.lock.common.lock.models.lock.UserHomeResponse

fun UserHomeResponse.old(): LocksListResponse {
    val response = LocksListResponse()

    response.locks = this.locks.map { it.old() }
    response.properties = this.properties.map { it.old() }
    response.success = this.success
    response.message = this.message
    response.assignment_id = this.assignmentId
    response.logout = this.logout
    response.is_paid = this.isPaid
    response.totalUnreadNotification = this.totalUnreadNotification

    return response
}

private fun Lock.old(): LocksListResponse.LocksModel {
    val lock = LocksListResponse.LocksModel()

    lock.assignment = this.assignment.old()
    lock.lock = this.oldLock()
    lock.owner_id = this.ownerId
    lock.unit_id = this.unitId
    lock.booking_number = this.bookingNumber
    lock.privacy = this.privacy
    lock.privacy_changed = this.privacyChanged
    lock.companyCheckin = this.companyCheckIn
    lock.checkin = this.checkIn
    lock.totalCheckins = this.totalCheckins
    lock.property_details = this.property.oldDetails()
    lock.passcodeId = this.passcodeId
    lock.passcode = this.passcode

    return lock
}

private fun Lock.oldLock(): LocksListResponse.LockModel {
    val lock = LocksListResponse.LockModel()

    lock._id = this.id
    lock.access_key = this.accessKey
    lock.battery_level = this.batteryLevel
    lock.createdAt = this.createdAt
    lock.image = this.image
    lock.lock_uid = this.lockUid
    lock.name = this.name
    lock.desc = this.desc
    lock.provider = this.provider
    lock.status = this.status
    lock.unique_key = this.uniqueKey
    lock.unit_id = this.unitId
    lock.time_zone = this.timeZone
    lock.internal_id = this.internalId
    lock.encrypted_key = this.encryptedKey
    lock.privacy_mode = this.privacyMode
    lock.primary = this.primary
    lock.privacy_permission = this.privacyPermission
    lock.privacy_owner = this.privacyOwner
    lock.icon = this.icons.map { it.old() }
    lock.property_details = this.property.oldDetails()
    lock.firmwareUpdated = this.firmwareUpdated
    lock.firmwareVersion = this.firmwareVersion
    lock.firmwareAvailableVersion = this.firmwareAvailableVersion
    lock.tedeeLockId = this.tedeeLockId

    if (lock.icon.isEmpty()) {
        lock.icon = listOf(LocksListResponse.IconModel())
    }

    return lock
}

private fun LocksListResponse.LocksModel.new(
    property: LocksListResponse.PropertiesMainModel
): Lock {
    return Lock(
        assignment = (assignment ?: LocksListResponse.AssignmentModel()).new(),
        ownerId = owner_id,
        unitId = unit_id,
        bookingNumber = booking_number,
        privacy = privacy,
        privacyChanged = privacy_changed,
        companyCheckIn = companyCheckin,
        checkIn = checkin,
        totalCheckins = totalCheckins,
        passcodeId = passcodeId,
        passcode = passcode,
        property = property_details.new(property),
        accessKey = lock.access_key,
        batteryLevel = lock.battery_level,
        createdAt = lock.createdAt,
        desc = lock.desc,
        encryptedKey = lock.encrypted_key,
        firmwareUpdated = lock.firmwareUpdated,
        id = lock._id,
        image = lock.image,
        internalId = lock.internal_id,
        name = lock.name,
        lockUid = lock.lock_uid,
        primary = lock.primary,
        privacyOwner = lock.privacy_owner,
        privacyPermission = lock.privacy_permission,
        privacyMode = lock.privacy_mode,
        provider = lock.provider,
        status = lock.status,
        timeZone = lock.time_zone,
        uniqueKey = lock.unique_key,
        icons = lock.icon.map { it.new() },
        firmwareVersion = lock.firmwareVersion,
        firmwareAvailableVersion = lock.firmwareAvailableVersion,
        tedeeLockId = lock.tedeeLockId
    )
}

private fun LockAssignment.old(): LocksListResponse.AssignmentModel {
    val assignment = LocksListResponse.AssignmentModel()

    assignment.assignment_data = this.oldData()
    assignment.time_ranges = this.timeRanges.map { it.old() }

    return assignment
}

private fun LockAssignment.oldData(): LocksListResponse.AssignmentDataModel {
    val data = LocksListResponse.AssignmentDataModel()

    data._id = this.id
    data.assigned_at = this.assignedAt
    data.assigned_by = this.assignedBy
    data.assigned_to = this.assignedTo
    data.lock_id = this.lockId
    data.time_profile_id = this.timeProfileId.old()
    data.valid_from = this.validFrom
    data.valid_to = this.validTo
    data.status = this.status

    return data
}

private fun LocksListResponse.AssignmentModel.new(): LockAssignment {
    return LockAssignment(
        id = assignment_data._id,
        assignedAt = assignment_data.assigned_at,
        assignedBy = assignment_data.assigned_by,
        assignedTo = assignment_data.assigned_to,
        lockId = assignment_data.lock_id,
        timeProfileId = assignment_data.time_profile_id.new(),
        validFrom = assignment_data.valid_from,
        validTo = assignment_data.valid_to,
        status = assignment_data.status,
        timeRanges = time_ranges.map { it.new() }
    )
}

private fun TimeProfileId.old(): LocksListResponse.TimeProfileIdModel {
    val id = LocksListResponse.TimeProfileIdModel()

    id._id = this.id
    id.created_at = this.createdAt
    id.created_by = this.createdBy
    id.iseo_id = this.iseoId.toInt()
    id.name = this.name
    id.status = this.status

    return id
}

private fun LocksListResponse.TimeProfileIdModel.new(): TimeProfileId {
    return TimeProfileId(
        id = _id,
        createdAt = created_at,
        createdBy = created_by,
        iseoId = iseo_id.toLong(),
        name = name,
        status = status
    )
}

private fun TimeRange.old(): LocksListResponse.TimeRangeModel {
    val range = LocksListResponse.TimeRangeModel()

    range._id = this.id
    range.allowed_days = this.allowedDays
    range.always_open = this.alwaysOpen
    range.created_at = this.createdAt
    range.holidays = this.holidays
    range.name = this.name
    range.routine_id = this.routineId
    range.status = this.status
    range.time_slot = this.oldTimeSlot()

    return range
}

private fun TimeRange.oldTimeSlot(): LocksListResponse.TimeSlotModel {
    val slot = LocksListResponse.TimeSlotModel()

    slot.start_hour = this.startHour
    slot.end_hour = this.endHour
    slot.start_min = this.startMin
    slot.end_min = this.endMin

    return slot
}

private fun LocksListResponse.TimeRangeModel.new(): TimeRange {
    return TimeRange(
        id = _id,
        allowedDays = allowed_days,
        alwaysOpen = always_open,
        createdAt = created_at,
        holidays = holidays,
        name = name,
        routineId = routine_id,
        status = status,
        startHour = time_slot.start_hour,
        startMin = time_slot.start_min,
        endHour = time_slot.end_hour,
        endMin = time_slot.end_min
    )
}

private fun Icon.old(): LocksListResponse.IconModel {
    val icon = LocksListResponse.IconModel()

    icon._id = this.id
    icon.name = this.name
    icon.icon = this.icon
    icon.type = this.type
    icon.createdAt = this.createdAt

    return icon
}

private fun LocksListResponse.IconModel.new(): Icon {
    return Icon(
        id = _id,
        name = name,
        icon = icon,
        type = type,
        createdAt = createdAt
    )
}

private fun Property.old(): LocksListResponse.PropertiesMainModel {
    val property = LocksListResponse.PropertiesMainModel()

    property._id = this.id
    property.latitude = this.latitude
    property.longitude = this.longitude
    property.manager_id = this.managerId
    property.manager_type = this.managerType
    property.emirate = this.emirate
    property.area = this.area
    property.building_name = this.buildingName
    property.total_floors = this.totalFloors
    property.created_at = this.createdAt
    property.icon_id = this.iconId
    property.support_call_number = this.supportCallNumber
    property.support_whatsapp_number = this.supportWhatsappNumber
    property.icon = this.icons.map { it.old() }
    property.count = this.count

    return property
}

private fun PropertyDetails.oldDetails(): LocksListResponse.PropertyDetailsModel {
    val details = LocksListResponse.PropertyDetailsModel()

    details.id = this.id
    details.floor = this.floor
    details.latitude = this.latitude
    details.longitude = this.longitude
    details.name = this.name
    details.room_number = this.roomNumber
    details.laundary_number = this.laundryNumber
    details.grocery_number = this.groceryNumber
    details.maintainance_number = this.maintenanceNumber
    details.appartment_number = this.apartmentNumber
    details.support_call_number = this.supportCallNumber
    details.support_whatsapp_number = this.supportWhatsappNumber
    details.map_id = this.mapId
    details.area = this.area
    details.building_name = this.buildingName
    details.icon = arrayListOf()

    return details
}

private fun LocksListResponse.PropertiesMainModel.new(
    details: LocksListResponse.PropertyDetailsModel
): Property {
    return Property(
        id = details.id,
        latitude = details.latitude,
        longitude = details.longitude,
        laundryNumber = details.laundary_number,
        groceryNumber = details.grocery_number,
        maintenanceNumber = details.maintainance_number,
        supportCallNumber = details.support_call_number,
        supportWhatsappNumber = details.support_whatsapp_number,
        area = details.area,
        buildingName = details.building_name,
        icons = details.icon.map { it.new() },
        count = count,
        createdAt = created_at,
        emirate = emirate,
        iconId = icon_id,
        managerId = manager_id,
        managerType = manager_type,
        totalFloors = total_floors
    )
}

private fun LocksListResponse.PropertyDetailsModel.new(main: LocksListResponse.PropertiesMainModel): PropertyDetails {
    return PropertyDetails(
        id = id,
        latitude = latitude,
        longitude = longitude,
        laundryNumber = laundary_number,
        groceryNumber = grocery_number,
        maintenanceNumber = maintainance_number,
        supportCallNumber = support_call_number,
        supportWhatsappNumber = support_whatsapp_number,
        area = area,
        buildingName = building_name,
        count = main.count,
        createdAt = main.created_at,
        emirate = main.emirate,
        iconId = main.icon_id,
        managerId = main.manager_id,
        managerType = main.manager_type,
        totalFloors = main.total_floors,
        floor = floor,
        name = name,
        roomNumber = room_number,
        apartmentNumber = appartment_number,
        mapId = map_id
    )
}