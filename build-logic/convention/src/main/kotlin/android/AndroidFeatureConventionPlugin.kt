@file:Suppress("PackageDirectoryMismatch")

import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

class AndroidFeatureConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) = with(target) {
        pluginManager.apply("keyless.android.library")
        pluginManager.apply("keyless.android.compose")

        dependencies {
            add("implementation", catalog.findLibrary("androidx.navigation.fragment").get())
            add("implementation", catalog.findLibrary("androidx.navigation.ui").get())
        }
    }
}
