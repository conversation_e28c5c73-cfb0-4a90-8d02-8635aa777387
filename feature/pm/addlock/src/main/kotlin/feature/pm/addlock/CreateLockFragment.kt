package feature.pm.addlock

import android.app.Activity
import android.app.Dialog
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.AdapterView
import android.widget.EditText
import android.widget.PopupMenu
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.google.gson.JsonObject
import data.network.android.ApiUtils
import data.network.android.GetAccessKeyModel
import data.network.android.models.IconModel
import data.network.android.models.ModelAdminInstaller
import data.network.android.models.ModelDataUnit
import data.network.android.models.PropertyAllResponse
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.ProgressDialogUtils
import feature.common.dialogs.defaultDialog
import data.utils.android.hideKeyboard
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import feature.common.icons.SelectIconAdapter
import keyless.feature.pm.addlock.databinding.FragmentCreateLockBinding

class CreateLockFragment :
    Fragment(),
    AdapterView.OnItemSelectedListener,
    SelectIconAdapter.SetIcon {
    private lateinit var scanData: GetAccessKeyModel.DataKeyModel
    private var whichPlace: String = ""
    private var iconId: String = ""
    private var arrayIcons: ArrayList<IconModel> = ArrayList()
    lateinit var adapter: SelectIconAdapter
    private var buildDataId: String = ""
    private lateinit var popup: PopupMenu
    private lateinit var mViewModel: CreateLockFragmentViewModel
    var isClicked = 0

    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            requireActivity()
        )
    }
    private lateinit var popupUnit: PopupMenu
    private var unitDataId: String = ""
    private var internalId: String = ""
    private var argumentList: ModelAdminInstaller.DataModelInstaller = ModelAdminInstaller.DataModelInstaller()
    private lateinit var binding: FragmentCreateLockBinding
    private var arrayUnit: ArrayList<ModelDataUnit> = ArrayList()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentCreateLockBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initz()
        clickEvent()
        setAdapter()
        observerInit()
    }

    private fun observerInit() {
        mViewModel.progress.observe(viewLifecycleOwner) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(requireContext(), true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        mViewModel.error.observe(viewLifecycleOwner) {
            requireContext().toast(it)
        }
    }

    private fun setAdapter() {
        adapter = SelectIconAdapter(requireContext(), listener = this)
    }

    private fun initz() {
        mViewModel = ViewModelProvider(this)[CreateLockFragmentViewModel::class.java]
        scanData = arguments?.getParcelable("scanLockModel")!!
        whichPlace = arguments?.getString("whichPlace")!!
        argumentList = arguments?.getParcelable("list")!!

        mViewModel.getAllIcons(sharePrefs.token).observe(viewLifecycleOwner) {
            for (i in it.icons) {
                if (i.type == "lock") {
                    arrayIcons.add(i)
                }
            }
        }

        if (whichPlace == "installer") {
            mViewModel.getAllUnits(sharePrefs.token, argumentList.company[0]._id)
                .observe(requireActivity()) {
                    arrayUnit = it.data
                    getUnitNumbers(it.data)
                }
        } else {
            mViewModel.getAllUnits(sharePrefs.token, "")
                .observe(requireActivity()) {
                    arrayUnit = it.data
                    getUnitNumbers(it.data)
                }
        }

        internalId = scanData.internal_id ?: ""
        binding.etBrand.setText(scanData.provider)
        if (whichPlace == "installer") {
            mViewModel.getProperty(sharePrefs.token, argumentList.company[0]._id).observe(
                requireActivity()
            ) {
                if (it.success!!) {
                    getBuildingSpinner(it.properties ?: arrayListOf())
                }
            }
        } else {
            mViewModel.hitGetCompanyProperty(sharePrefs.token).observe(viewLifecycleOwner) {
                getBuildingSpinner(it.properties ?: arrayListOf())
            }
        }
    }

    private fun getBuildingSpinner(arrayList: ArrayList<PropertyAllResponse.Property>) {
        popup = PopupMenu(requireContext(), binding.spinnerBuilding, keyless.feature.common.R.style.PopupMenu)
        for (i in 0 until (arrayList.size)) {
            popup.menu.add(arrayList[i].building_name)
        }
        popup.setOnMenuItemClickListener { item ->
            buildDataId =
                arrayList[arrayList.map { (it.building_name ?: "") }.indexOf(item.title)]._id!!
            binding.spinnerBuilding.setText(item.title)
            true
        }
    }

    private fun getUnitNumbers(arrayList: ArrayList<ModelDataUnit>) {
        popupUnit = PopupMenu(requireActivity(), binding.etUnitNumber, keyless.feature.common.R.style.PopupMenu)
        popupUnit.gravity = Gravity.START
        for (i in 0 until (arrayList.size)) {
            popupUnit.menu.add(arrayList[i].unit_number)
        }
        popupUnit.setOnMenuItemClickListener { item ->
            val _id = arrayList[arrayList.map { it.unit_number }.indexOf(item.title)]._id
            unitDataId = _id
            binding.etUnitNumber.setText(item.title)
            binding.etApartmentNumber.setText(item.title)
            binding.etApartmentNumber.isClickable = false
            binding.etApartmentNumber.isEnabled = false
            binding.clearUnitNumber.isVisible = true
            true
        }
    }

    private fun clickEvent() {
        binding.clearUnitNumber.setOnClickListener {
            unitDataId = ""
            binding.etUnitNumber.setText("")
            binding.etApartmentNumber.setText("")
            binding.etApartmentNumber.isClickable = true
            binding.etApartmentNumber.isEnabled = true
            binding.clearUnitNumber.isVisible = false
        }

        binding.etUnitNumber.setOnClickListener {
            if (isClicked == 0) {
                isClicked = 1
                if (arrayUnit.size > 0) {
                    isClicked = 0
                    popupUnit.show()
                } else {
                    defaultDialog(
                        requireActivity(),
                        "No Units Found.",
                        object : OnActionOK {
                            override fun onClickData() {
                                isClicked = 0
                            }
                        }
                    )
                }
            }
        }

        binding.ivBack.setOnClickListener {
            requireActivity().finish()
        }

        binding.cvIcon.setOnClickListener {
            selectIconDialog(arrayIcons, adapter)
        }

        binding.spinnerBuilding.setOnClickListener {
            requireActivity().hideKeyboard()
            popup.show()
        }

        binding.btnSaveEditDetails.setOnClickListener {
            val jsonObject = JsonObject()
            jsonObject.addProperty("internal_id", internalId)
            jsonObject.addProperty("floor_number", binding.etFloorNumber.text.toString())
            jsonObject.addProperty("appartment_number", binding.etApartmentNumber.text.toString())
            jsonObject.addProperty("room_number", binding.etApartmentNumber.text.toString())
            jsonObject.addProperty("lock_name", binding.etLockName.text.toString())
            jsonObject.addProperty("status", "1")
            jsonObject.addProperty("propertyId", buildDataId)
            jsonObject.addProperty("icon_id", iconId)
            jsonObject.addProperty("unit_id", unitDataId)
            var companyId = ""
            if (whichPlace == "installer") {
                if (argumentList.company[0]._id.isNotEmpty()) {
                    companyId = argumentList.company[0]._id
                }
            }

            if (validation()) {
                mViewModel.hitAssignLockApi(sharePrefs.token, jsonObject, whichPlace, companyId).observe(
                    viewLifecycleOwner
                ) {
                    if (it.success) {
                        if (whichPlace == "installer") {
                            val jsonObject = JsonObject()
                            jsonObject.addProperty("status", "1")
                            jsonObject.addProperty("company_id", argumentList.company[0]._id)
                            jsonObject.addProperty("lock_id", scanData._id)
                            jsonObject.addProperty("installation_id", argumentList._id)
                            jsonObject.addProperty("comment", "success")
                            ProgressDialogUtils.getInstance().showProgress(requireActivity(), true)
                            mViewModel.updateStatus(sharePrefs.token, jsonObject).observe(
                                requireActivity()
                            ) {
                                ProgressDialogUtils.getInstance().hideProgress()
                                requireActivity().setResult(Activity.RESULT_OK)
                                requireActivity().finish()
                            }
                        } else if (!scanData.installationId.isNullOrBlank()) {
                            val jsonObject = JsonObject()
                            jsonObject.addProperty("status", "1")
                            jsonObject.addProperty("company_id", "")
                            jsonObject.addProperty("lock_id", scanData._id)
                            jsonObject.addProperty("installation_id", scanData.installationId)
                            jsonObject.addProperty("comment", "Installation Done")
                            ProgressDialogUtils.getInstance().showProgress(requireActivity(), true)

                            mViewModel.hitUpdateInstallationStatus(
                                token = sharePrefs.token,
                                data = jsonObject
                            ).observe(viewLifecycleOwner) {
                                if (it.success) {
                                    ProgressDialogUtils.getInstance().hideProgress()
                                    requireActivity().setResult(Activity.RESULT_OK)
                                    requireActivity().finish()
                                } else {
                                    ProgressDialogUtils.getInstance().hideProgress()
                                    defaultDialog(
                                        requireActivity(),
                                        it.message,
                                        object : OnActionOK {
                                            override fun onClickData() {
                                                requireActivity().setResult(Activity.RESULT_OK)
                                                requireActivity().finish()
                                            }
                                        }
                                    )
                                }
                            }
                        } else {
                            requireActivity().setResult(Activity.RESULT_OK)
                            requireActivity().finish()
                        }
                    } else {
                        defaultDialog(
                            requireActivity(),
                            it.message,
                            object : OnActionOK {
                                override fun onClickData() {
                                    requireActivity().setResult(Activity.RESULT_OK)
                                    requireActivity().finish()
                                }
                            }
                        )
                    }
                }
            }
        }
    }

    private fun validation(): Boolean {
        if (iconId.isEmpty()) {
            requireActivity().toast(getString(keyless.data.utils.android.R.string.please_select_icon))
            return false
        } else if (binding.etLockName.text.toString().isEmpty()) {
            requireActivity().toast(getString(keyless.data.utils.android.R.string.please_enter_lock_name))
            return false
        } else if (buildDataId.isEmpty()) {
            requireActivity().toast(getString(keyless.data.utils.android.R.string.please_select_building_name))
            return false
        } else if (binding.etFloorNumber.text.toString().isEmpty()) {
            requireActivity().toast(getString(keyless.data.utils.android.R.string.please_enter_floor_number))
            return false
        } else if (binding.etApartmentNumber.text.toString().isEmpty()) {
            requireActivity().toast(getString(keyless.data.utils.android.R.string.please_enter_apartment_number))
            return false
        } else {
            return true
        }
    }

    override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
        parent?.getItemAtPosition(position).toString()
    }

    override fun onNothingSelected(parent: AdapterView<*>?) {
    }

    private fun selectIconDialog(
        arrayIcons: ArrayList<IconModel>,
        adapter: SelectIconAdapter
    ) {
        val dialog = context?.let { Dialog(it) }
        dialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog?.setContentView(keyless.feature.common.R.layout.select_icon_dialog)
        dialog?.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
            val displayRectangle = Rect()
            val window = dialog.window
            v.getWindowVisibleDisplayFrame(displayRectangle)
            val maxHeight = displayRectangle.height() * 0.9f // 60%
            val maxWidth = displayRectangle.width() * 0.9f // 60%
            window?.setLayout(maxWidth.toInt(), maxHeight.toInt())
        }

        dialog?.setCancelable(true)
        dialog?.setCanceledOnTouchOutside(true)
        dialog?.window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN)
        var rv_icon = dialog.findViewById<RecyclerView>(keyless.feature.common.R.id.rv_icon)
        var btn_save = dialog.findViewById<TextView>(keyless.feature.common.R.id.btn_save)
        var sv_lock = dialog.findViewById<EditText>(keyless.feature.common.R.id.sv_lock)

        rv_icon.layoutManager = GridLayoutManager(requireActivity(), 3)
        rv_icon.adapter = adapter
        setUpIconRecyclerView(dialog, arrayIcons, adapter)
        btn_save!!.setOnClickListener {
            dialog.dismiss()
        }
        sv_lock.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                adapter.filter.filter(p0.toString())
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })

        dialog.show()
    }

    private fun setUpIconRecyclerView(
        dialog: Dialog,
        arrayIcons: ArrayList<IconModel>,
        adapter: SelectIconAdapter
    ) {
        adapter.updateValues(arrayIcons, dialog)
    }

    override fun getIcon(iconModelAll: IconModel) {
        binding.ivIcon.setImageResource(0)
        iconId = iconModelAll._id
        if (iconModelAll.icon.isNotEmpty()) {
            Glide.with(requireActivity())
                .load(ApiUtils.IMAGE_BASE_URL + iconModelAll.icon)
                .placeholder(keyless.feature.common.R.drawable.iv_other_icon)
                .into(binding.ivIcon)
            binding.tvPress.text = getString(keyless.data.utils.android.R.string.change_icon)
        }
    }
}