<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:layout_width="160dp"
    android:layout_height="160dp"
    android:layout_marginBottom="10dp"
    card_view:cardUseCompatPadding="true"
    card_view:cardCornerRadius="10dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        card_view:layout_constraintBottom_toBottomOf="parent"
        card_view:layout_constraintEnd_toEndOf="parent"
        card_view:layout_constraintStart_toStartOf="parent"
        card_view:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/imageView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop"
            card_view:layout_constraintBottom_toBottomOf="parent"
            card_view:layout_constraintEnd_toEndOf="parent"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvServiceName"
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:background="@drawable/services_text_bg"
            android:fontFamily="@font/poppins_regular_400"
            android:gravity="center"
            android:text="@string/living_room_lock"
            android:textColor="@color/white"
            android:textSize="10dp"
            card_view:layout_constraintBottom_toBottomOf="parent"
            card_view:layout_constraintEnd_toEndOf="parent"
            card_view:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>