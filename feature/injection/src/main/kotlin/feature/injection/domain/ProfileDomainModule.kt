package feature.injection.domain

import domain.settings.profile.ViewModel
import domain.settings.profile.models.SettingsProfileDomainScope
import domain.settings.profile.repositories.ScreenStateRepository
import domain.settings.profile.repositories.SideEffectsRepository
import org.koin.dsl.module

internal val profileDomainModule = module {
    scope<SettingsProfileDomainScope> {

        scoped {
            SideEffectsRepository()
        }

        scoped {
            ScreenStateRepository(
                status = get()
            )
        }

        scoped {
            ViewModel(
                logger = get(),
                userAccount = get(),
                screenState = get(),
                sideEffects = get(),
                status = get()
            )
        }
    }
}