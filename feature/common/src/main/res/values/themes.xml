<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/white</item>
        <item name="colorPrimaryDark">@color/white</item>
        <item name="colorAccent">@color/colorAccent</item>
        <!--        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>-->
        <item name="android:statusBarColor">@color/colorAccent</item>
        <item name="colorSecondary">@color/colorAccent</item>
        <item name="colorSecondaryVariant">@color/colorAccent</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:windowDisablePreview">true</item>
    </style>

    <style name="ImageMirror">
        <item name="android:scaleX">1</item>
    </style>

    <style name="mirrorText">
        <item name="android:textDirection">firstStrongLtr</item>
        <item name="android:textAlignment">viewStart</item>
    </style>


    <style name="Theme.Transparent" parent="Theme.AppCompat.Light.NoActionBar">
        <!--        <item name="android:windowFullscreen">true</item>-->
        <!--        <item name="android:windowTranslucentStatus">true</item>-->
        <!--        <item name="android:windowTranslucentNavigation">true</item>-->

        <item name="android:windowNoTitle">true</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:statusBarColor">@color/colorAccent</item>
        <item name="android:windowBackground">@color/white</item>
        <item name="android:colorBackground">@color/white</item>
    </style>

    <style name="BottomNavigationView.Active" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">10dp</item>
        <item name="android:paddingBottom">100dip</item>
        <item name="android:fontFamily">@font/poppins_regular_400</item>
    </style>

    <style name="BottomNavigationView" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">10dp</item>
        <item name="android:paddingBottom">100dip</item>
        <item name="android:fontFamily">@font/poppins_regular_400</item>
    </style>


    <style name="PopupMenu" parent="android:Widget.Holo.Light.ListPopupWindow">
        <item name="android:popupBackground">#ffffff</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:gravity">start</item>
    </style>

    <style name="buttonStyle" parent="@style/Widget.AppCompat.TextView">
        <item name="android:textColor">@color/black</item>
        <item name="android:fontFamily">@font/poppins_medium_500</item>
        <item name="android:background">@drawable/bg_btn_round</item>
        <item name="android:textSize">18dp</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="trans_bg_dialog" parent="@style/Theme.AppCompat.Dialog">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowSoftInputMode">adjustResize</item>

    </style>


    <style name="SCBSwitch" parent="Theme.AppCompat.Light">
        <!-- active thumb & track color (30% transparency) -->
        <item name="colorControlActivated">@color/colorAccent</item>

        <!-- inactive thumb color -->
        <item name="colorSwitchThumbNormal">@color/color_grey</item>

        <!-- inactive track color (30% transparency) -->
        <item name="android:colorForeground">@color/card_stroke_access</item>
    </style>

    <style name="progressBarBlue" parent="@style/Theme.AppCompat">
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <!-- Base application theme. -->

</resources>