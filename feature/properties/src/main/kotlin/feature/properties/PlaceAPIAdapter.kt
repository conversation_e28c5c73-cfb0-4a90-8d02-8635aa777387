package feature.properties

import android.app.Activity
import android.content.Context
import android.location.Address
import android.location.Geocoder
import android.widget.ArrayAdapter
import android.widget.Filter
import android.widget.Filterable
import com.google.android.gms.maps.model.LatLng
import java.util.ArrayList

class PlaceAPIAdapter(
    val mContext: Context,
    mResource: Int,
    val activity: Activity
) : ArrayAdapter<String>(mContext, mResource), Filterable {

    var resultList: ArrayList<String>? = null
    var mPlaceAPI = PlaceAPI()

    override fun getCount(): Int {
        return if (resultList == null) 0 else resultList!!.size
    }

    override fun getItem(position: Int): String? {
        return resultList!![position]
    }

    override fun getFilter(): Filter {
        return object : Filter() {
            override fun performFiltering(constraint: CharSequence?): FilterResults? {
                try {
                    val filterResults = FilterResults()
                    if (constraint != null) {
                        resultList = mPlaceAPI.autocomplete(constraint.toString(), mContext)
                        filterResults.values = resultList
                        filterResults.count = resultList!!.size
                        return filterResults
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }

                return null
            }

            override fun publishResults(constraint: CharSequence?, results: FilterResults?) {
                try {
                    if (results != null && results.count > 0) {
                        activity.runOnUiThread { notifyDataSetChanged() }
                    } else {
                        notifyDataSetInvalidated()
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }
    fun getLocationFormAddress(context: Context, address: String): LatLng? {
        val coder = Geocoder(context)
        val addresses: List<Address>?
        try {
            addresses = coder.getFromLocationName(address, 5)
            val location = addresses!![0]
            if (location != null) {
                return LatLng(location.latitude, location.longitude)
            }
        } catch (ex: Exception) {
            ex.printStackTrace()
        }

        return null
    }
}